package com.tool.converge.api.web.controller.test;

import cn.hutool.core.util.StrUtil;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.business.dingtalk.service.DingTalkMessageService;
import com.tool.converge.business.sms.service.SmsService;
import com.tool.converge.business.sms.service.SmsCallbackService;
import com.tool.converge.repository.domain.sms.SmsCallbackRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息发送测试控制器
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@RestController
@RequestMapping("/test/message")
@Tag(name = "消息发送测试", description = "用于测试钉钉和短信发送功能")
public class MessageTestController {

    @Resource
    private DingTalkMessageService dingTalkMessageService;

    @Resource
    private SmsService smsService;

    @Resource
    private SmsCallbackService smsCallbackService;

    @ApiResponse
    @PostMapping("/dingtalk/send")
    @Operation(summary = "测试发送钉钉消息", description = "发送钉钉文本消息到指定机器人")
    @PrintLog("测试发送钉钉消息")
    public String sendDingTalkMessage(
            @Parameter(description = "钉钉机器人Webhook地址", required = true)
            @RequestParam String webhookUrl,
            @Parameter(description = "消息内容", required = true)
            @RequestParam String content,
            @Parameter(description = "需要@的手机号列表，多个用逗号分隔", required = false)
            @RequestParam(required = false) String atMobiles) {

        // 解析@手机号列表
        List<String> atMobileList = null;
        if (StrUtil.isNotBlank(atMobiles)) {
            atMobileList = Arrays.stream(atMobiles.split(","))
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
        }

        // 发送钉钉消息
        webhookUrl="https://oapi.dingtalk.com/robot/send?access_token=29b706159a6d2340282b3a4b667b7abf849e0b992dfb787f738360e0ad67edba,https://oapi.dingtalk.com/robot/send?access_token=da84028765469c797853f04c7f3ea129d81301a57ee21a3683748d8cbfaada6f";
        dingTalkMessageService.sendTextMessage(webhookUrl, content, atMobileList);

        log.info("钉钉消息发送成功，webhook: {}, 内容: {}, @手机号: {}", webhookUrl, content, atMobileList);
        return "钉钉消息发送成功";
    }

    @ApiResponse
    @PostMapping("/sms/batch")
    @Operation(summary = "测试批量发送模板短信", description = "批量发送模板短信到多个手机号")
    @PrintLog("测试批量发送模板短信")
    public String sendBatchSms(
            @Parameter(description = "手机号列表，多个用逗号分隔", required = true)
            @RequestParam String phones,
            @Parameter(description = "模板参数，格式：key1=value1,key2=value2", required = false)
            @RequestParam(required = false) String templateParams,
            @Parameter(description = "用户回传数据", required = false)
            @RequestParam(required = false) String callData) {

        // 解析手机号列表
        List<String> phoneList = Arrays.stream(phones.split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (phoneList.isEmpty()) {
            throw new RuntimeException("手机号列表不能为空");
        }

        // 解析模板参数
        Map<String, String> paramsMap = parseTemplateParams(templateParams);

        boolean success = smsService.sendBatchSms(phoneList, paramsMap, callData);

        log.info("批量短信发送完成，总数: {}, 发送结果: {}", phoneList.size(), success ? "成功" : "失败");
        return String.format("批量短信发送完成，总数: %d, 发送结果: %s", phoneList.size(), success ? "成功" : "失败");
    }

    @ApiResponse
    @PostMapping("/mixed/send")
    @Operation(summary = "测试混合发送", description = "同时发送钉钉消息和模板短信")
    @PrintLog("测试混合发送")
    public String sendMixedMessage(
            @Parameter(description = "钉钉机器人Webhook地址", required = true)
            @RequestParam String webhookUrl,
            @Parameter(description = "手机号列表，多个用逗号分隔", required = true)
            @RequestParam String phones,
            @Parameter(description = "钉钉消息内容", required = true)
            @RequestParam String content,
            @Parameter(description = "短信模板参数，格式：key1=value1,key2=value2", required = false)
            @RequestParam(required = false) String templateParams) {

        // 解析手机号列表
        List<String> phoneList = Arrays.stream(phones.split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (phoneList.isEmpty()) {
            throw new RuntimeException("手机号列表不能为空");
        }

        // 发送钉钉消息
        dingTalkMessageService.sendTextMessage(webhookUrl, content, phoneList);
        log.info("钉钉消息发送完成");

        // 发送短信
        Map<String, String> paramsMap = parseTemplateParams(templateParams);
        boolean smsSuccess = smsService.sendBatchSms(phoneList, paramsMap);
        log.info("短信发送完成，结果: {}", smsSuccess ? "成功" : "失败");

        return String.format("混合发送完成，钉钉消息已发送，短信发送结果: %s", smsSuccess ? "成功" : "失败");
    }

    @ApiResponse
    @PostMapping("/sms/callback/mock")
    @Operation(summary = "模拟短信回调", description = "模拟短信服务商的回调通知")
    @PrintLog("模拟短信回调")
    public String mockSmsCallback(
            @Parameter(description = "消息ID（requestNo）", required = true)
            @RequestParam String msgId,
            @Parameter(description = "手机号", required = true)
            @RequestParam String phone,
            @Parameter(description = "状态：DELIVRD_SUCCESS/DELIVRD_FAIL/SEND_FAIL", required = true)
            @RequestParam String state,
            @Parameter(description = "状态描述", required = false)
            @RequestParam(required = false) String stateMsg,
            @Parameter(description = "用户回传数据", required = false)
            @RequestParam(required = false) String callData) {

        // 构建回调请求
        SmsCallbackRequest request = SmsCallbackRequest.builder()
                .msgId(msgId)
                .phone(phone)
                .state(state)
                .stateMsg(stateMsg)
                .receiveTime(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .success("DELIVRD_SUCCESS".equals(state))
                .callData(callData)
                .smsChannelCode("TEST_CHANNEL")
                .smsChannelCodeDesc("测试通道")
                .build();

        // 处理回调
        smsCallbackService.handleCallback(request);

        log.info("模拟短信回调完成，msgId: {}, phone: {}, state: {}", msgId, phone, state);
        return "模拟短信回调处理完成";
    }

    @ApiResponse
    @GetMapping("/config/info")
    @Operation(summary = "获取配置信息", description = "获取当前短信和钉钉的配置信息")
    @PrintLog("获取配置信息")
    public String getConfigInfo() {
        // 这里可以返回一些配置信息用于调试
        return "配置信息获取成功，请查看日志";
    }

    /**
     * 解析模板参数
     *
     * @param templateParams 模板参数字符串，格式：key1=value1,key2=value2
     * @return 模板参数Map
     */
    private Map<String, String> parseTemplateParams(String templateParams) {
        Map<String, String> paramsMap = new HashMap<>();

        if (StrUtil.isNotBlank(templateParams)) {
            String[] pairs = templateParams.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    paramsMap.put(keyValue[0].trim(), keyValue[1].trim());
                }
            }
        }

        // 如果没有提供参数，使用默认的测试参数
        if (paramsMap.isEmpty()) {
            paramsMap.put("content", "这是一条测试短信");
            paramsMap.put("code", "123456");
        }

        return paramsMap;
    }
}
