package com.tool.converge.api.web.controller.test;

import cn.hutool.core.util.StrUtil;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.converge.business.dingtalk.service.DingTalkMessageService;
import com.tool.converge.business.sms.service.SmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 消息发送测试控制器
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@RestController
@RequestMapping("/test/message")
@Tag(name = "消息发送测试", description = "用于测试钉钉和短信发送功能")
public class MessageTestController {

    @Resource
    private DingTalkMessageService dingTalkMessageService;

    @Resource
    private SmsService smsService;

    @ApiResponse
    @PostMapping("/dingtalk/send")
    @Operation(summary = "测试发送钉钉消息", description = "发送钉钉文本消息到指定机器人")
    @PrintLog("测试发送钉钉消息")
    public String sendDingTalkMessage(
            @Parameter(description = "钉钉机器人Webhook地址", required = true)
            @RequestParam String webhookUrl,
            @Parameter(description = "消息内容", required = true)
            @RequestParam String content,
            @Parameter(description = "需要@的手机号列表，多个用逗号分隔", required = false)
            @RequestParam(required = false) String atMobiles) {

        // 解析@手机号列表
        List<String> atMobileList = null;
        if (StrUtil.isNotBlank(atMobiles)) {
            atMobileList = Arrays.stream(atMobiles.split(","))
                    .map(String::trim)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
        }

        // 发送钉钉消息
        dingTalkMessageService.sendTextMessage(webhookUrl, content, atMobileList);

        log.info("钉钉消息发送成功，webhook: {}, 内容: {}, @手机号: {}", webhookUrl, content, atMobileList);
        return "钉钉消息发送成功";
    }

    @ApiResponse
    @PostMapping("/sms/send")
    @Operation(summary = "测试发送短信", description = "发送短信到指定手机号")
    @PrintLog("测试发送短信")
    public String sendSms(
            @Parameter(description = "手机号", required = true)
            @RequestParam String phone,
            @Parameter(description = "短信内容", required = true)
            @RequestParam String content,
            @Parameter(description = "用户回传数据", required = false)
            @RequestParam(required = false) String callData) {

        boolean success = smsService.sendSms(phone, content, callData);

        if (success) {
            log.info("短信发送成功，手机号: {}, 内容: {}", phone, content);
            return "短信发送成功";
        } else {
            log.warn("短信发送失败，手机号: {}, 内容: {}", phone, content);
            throw new RuntimeException("短信发送失败");
        }
    }

    @ApiResponse
    @PostMapping("/sms/batch")
    @Operation(summary = "测试批量发送短信", description = "批量发送短信到多个手机号")
    @PrintLog("测试批量发送短信")
    public String sendBatchSms(
            @Parameter(description = "手机号列表，多个用逗号分隔", required = true)
            @RequestParam String phones,
            @Parameter(description = "短信内容", required = true)
            @RequestParam String content,
            @Parameter(description = "用户回传数据", required = false)
            @RequestParam(required = false) String callData) {

        // 解析手机号列表
        List<String> phoneList = Arrays.stream(phones.split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (phoneList.isEmpty()) {
            throw new RuntimeException("手机号列表不能为空");
        }

        int successCount = smsService.sendBatchSms(phoneList, content, callData);

        log.info("批量短信发送完成，总数: {}, 成功: {}", phoneList.size(), successCount);
        return String.format("批量短信发送完成，总数: %d, 成功: %d", phoneList.size(), successCount);
    }

    @ApiResponse
    @PostMapping("/mixed/send")
    @Operation(summary = "测试混合发送", description = "同时发送钉钉消息和短信")
    @PrintLog("测试混合发送")
    public String sendMixedMessage(
            @Parameter(description = "钉钉机器人Webhook地址", required = true)
            @RequestParam String webhookUrl,
            @Parameter(description = "手机号列表，多个用逗号分隔", required = true)
            @RequestParam String phones,
            @Parameter(description = "消息内容", required = true)
            @RequestParam String content) {

        // 解析手机号列表
        List<String> phoneList = Arrays.stream(phones.split(","))
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());

        if (phoneList.isEmpty()) {
            throw new RuntimeException("手机号列表不能为空");
        }

        // 发送钉钉消息
        dingTalkMessageService.sendTextMessage(webhookUrl, content, phoneList);
        log.info("钉钉消息发送完成");

        // 发送短信
        int successCount = smsService.sendBatchSms(phoneList, content);
        log.info("短信发送完成，成功: {}", successCount);

        return String.format("混合发送完成，钉钉消息已发送，短信成功: %d/%d", successCount, phoneList.size());
    }

    @ApiResponse
    @GetMapping("/config/info")
    @Operation(summary = "获取配置信息", description = "获取当前短信和钉钉的配置信息")
    @PrintLog("获取配置信息")
    public String getConfigInfo() {
        // 这里可以返回一些配置信息用于调试
        return "配置信息获取成功，请查看日志";
    }
}
