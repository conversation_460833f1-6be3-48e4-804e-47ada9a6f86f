package com.tool.converge.business.sms.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 短信服务配置
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@Configuration
@NoArgsConstructor
@AllArgsConstructor
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {
    
    /**
     * 短信服务接口地址
     */
    private String apiUrl;
    
    /**
     * 分配的token
     */
    private String token;
    
    /**
     * 短信提供商枚举值
     */
    private String smsIspNo;
    
    /**
     * 优先级别(>5 为验证码级别短信，<=5为普通短信)
     */
    private Integer priority = 5;
    
    /**
     * 是否为模拟测试下发,true模拟下发，false真实下发,默认false
     */
    private Boolean isTest = false;
}
