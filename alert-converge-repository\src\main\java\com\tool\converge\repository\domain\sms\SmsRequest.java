package com.tool.converge.repository.domain.sms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 短信发送请求对象
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分配的token
     */
    private String token;

    /**
     * 短信接收手机号
     */
    private String phone;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 短信提供商枚举值
     */
    private String smsIspNo;

    /**
     * 请求流水号
     */
    private String requestNo;

    /**
     * 优先级别(>5 为验证码级别短信，<=5为普通短信)
     */
    private Integer priority;

    /**
     * 是否为模拟测试下发,true模拟下发，false真实下发,默认false
     */
    private Boolean isTest;

    /**
     * 用户回传数据
     */
    private String callData;
}
