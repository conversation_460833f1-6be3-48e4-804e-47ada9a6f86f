package com.tool.converge.business.sms.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.tool.converge.business.sms.config.SmsConfig;
import com.tool.converge.repository.domain.sms.SmsRequest;
import com.tool.converge.repository.domain.sms.SmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;

/**
 * 短信发送服务
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@Service
public class SmsService {

    @Resource
    private SmsConfig smsConfig;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 发送短信给单个手机号
     *
     * @param phone   手机号
     * @param content 短信内容
     * @return 发送结果
     */
    public boolean sendSms(String phone, String content) {
        return sendSms(phone, content, null);
    }

    /**
     * 发送短信给单个手机号
     *
     * @param phone    手机号
     * @param content  短信内容
     * @param callData 用户回传数据
     * @return 发送结果
     */
    public boolean sendSms(String phone, String content, String callData) {
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(content)) {
            log.warn("短信发送失败：手机号或内容为空");
            return false;
        }

        // 构建请求对象
        SmsRequest request = SmsRequest.builder()
                .token(smsConfig.getToken())
                .phone(phone)
                .content(content)
                .smsIspNo(smsConfig.getSmsIspNo())
                .requestNo(IdUtil.fastSimpleUUID())
                .priority(smsConfig.getPriority())
                .isTest(smsConfig.getIsTest())
                .callData(callData)
                .build();

        return sendSmsRequest(request);
    }

    /**
     * 批量发送短信
     *
     * @param phones  手机号列表
     * @param content 短信内容
     * @return 发送成功的数量
     */
    public int sendBatchSms(List<String> phones, String content) {
        return sendBatchSms(phones, content, null);
    }

    /**
     * 批量发送短信
     *
     * @param phones   手机号列表
     * @param content  短信内容
     * @param callData 用户回传数据
     * @return 发送成功的数量
     */
    public int sendBatchSms(List<String> phones, String content, String callData) {
        if (phones == null || phones.isEmpty()) {
            log.warn("短信批量发送失败：手机号列表为空");
            return 0;
        }

        int successCount = 0;
        for (String phone : phones) {
            if (StrUtil.isNotBlank(phone)) {
                try {
                    if (sendSms(phone.trim(), content, callData)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("发送短信到手机号 {} 失败", phone, e);
                }
            }
        }

        log.info("批量发送短信完成，总数：{}，成功：{}", phones.size(), successCount);
        return successCount;
    }

    /**
     * 发送短信请求
     *
     * @param request 短信请求对象
     * @return 发送结果
     */
    private boolean sendSmsRequest(SmsRequest request) {
        try {
            log.info("开始发送短信，手机号：{}，请求流水号：{}", request.getPhone(), request.getRequestNo());

            // 构建HTTP请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求实体
            HttpEntity<SmsRequest> entity = new HttpEntity<>(request, headers);

            // 发送HTTP请求
            ResponseEntity<SmsResponse> response = restTemplate.postForEntity(
                    smsConfig.getApiUrl(), entity, SmsResponse.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                SmsResponse smsResponse = response.getBody();
                log.info("短信发送响应：{}", JSON.toJSONString(smsResponse));

                // 判断业务是否成功（通常 "0000" 表示成功）
                if ("0000".equals(smsResponse.getCode())) {
                    log.info("短信发送成功，手机号：{}，流水号：{}", request.getPhone(), smsResponse.getRequestNo());
                    return true;
                } else {
                    log.warn("短信发送失败，手机号：{}，错误码：{}，错误信息：{}", 
                            request.getPhone(), smsResponse.getCode(), smsResponse.getMsg());
                    return false;
                }
            } else {
                log.error("短信发送HTTP请求失败，状态码：{}", response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            log.error("短信发送异常，手机号：{}，请求流水号：{}", request.getPhone(), request.getRequestNo(), e);
            return false;
        }
    }
}
