package com.tool.converge.business.sms.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.tool.converge.business.sms.config.SmsConfig;
import com.tool.converge.repository.domain.sms.SmsRequest;
import com.tool.converge.repository.domain.sms.SmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 模板短信发送服务
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Slf4j
@Service
public class SmsService {

    @Resource
    private SmsConfig smsConfig;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 批量发送模板短信（所有手机号使用相同的模板参数）
     *
     * @param phones     手机号列表
     * @param paramsMap  模板参数Map (key: paramsCode, value: paramsValue)
     * @return 发送结果
     */
    public boolean sendBatchSms(List<String> phones, Map<String, String> paramsMap) {
        return sendBatchSms(phones, paramsMap, null);
    }

    /**
     * 批量发送模板短信（所有手机号使用相同的模板参数）
     *
     * @param phones     手机号列表
     * @param paramsMap  模板参数Map (key: paramsCode, value: paramsValue)
     * @param callData   用户回传数据
     * @return 发送结果
     */
    public boolean sendBatchSms(List<String> phones, Map<String, String> paramsMap, String callData) {
        if (phones == null || phones.isEmpty()) {
            log.warn("短信批量发送失败：手机号列表为空");
            return false;
        }

        // 构建发送列表
        List<SmsRequest.SendItem> sendList = new ArrayList<>();
        for (String phone : phones) {
            if (StrUtil.isNotBlank(phone)) {
                sendList.add(buildSendItem(phone.trim(), paramsMap));
            }
        }

        if (sendList.isEmpty()) {
            log.warn("短信批量发送失败：没有有效的手机号");
            return false;
        }

        // 构建请求对象
        SmsRequest request = SmsRequest.builder()
                .sendList(sendList)
                .requestNo(IdUtil.fastSimpleUUID())
                .systemCode(smsConfig.getSystemCode())
                .templateCode(smsConfig.getTemplateCodeAlertMessage())
                .token(smsConfig.getToken())
                .isTest(smsConfig.getIsTest())
                .callData(callData)
                .build();

        return sendSmsRequest(request);
    }

    /**
     * 构建发送项
     *
     * @param phone     手机号
     * @param paramsMap 模板参数Map
     * @return 发送项
     */
    private SmsRequest.SendItem buildSendItem(String phone, Map<String, String> paramsMap) {
        List<SmsRequest.ParamsItem> paramsList = new ArrayList<>();

        if (paramsMap != null && !paramsMap.isEmpty()) {
            for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
                paramsList.add(SmsRequest.ParamsItem.builder()
                        .paramsCode(entry.getKey())
                        .paramsValue(entry.getValue())
                        .build());
            }
        }

        return SmsRequest.SendItem.builder()
                .phone(phone)
                .paramsList(paramsList)
                .build();
    }

    /**
     * 发送短信请求
     *
     * @param request 短信请求对象
     * @return 发送结果
     */
    private boolean sendSmsRequest(SmsRequest request) {
        try {
            // 获取手机号列表用于日志
            List<String> phoneList = request.getSendList().stream()
                    .map(SmsRequest.SendItem::getPhone)
                    .collect(java.util.stream.Collectors.toList());

            log.info("开始发送模板短信，手机号：{}，请求流水号：{}", phoneList, request.getRequestNo());

            // 构建HTTP请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 构建请求实体
            HttpEntity<SmsRequest> entity = new HttpEntity<>(request, headers);

            // 发送HTTP请求
            ResponseEntity<SmsResponse> response = restTemplate.postForEntity(
                    smsConfig.getApiUrl(), entity, SmsResponse.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                SmsResponse smsResponse = response.getBody();
                log.info("短信发送响应：{}", JSON.toJSONString(smsResponse));

                // 判断业务是否成功（通常 "0000" 表示成功）
                if ("0000".equals(smsResponse.getCode())) {
                    log.info("短信发送成功，手机号：{}，流水号：{}", phoneList, request.getRequestNo());
                    return true;
                } else {
                    log.error("短信发送失败，手机号：{}，错误码：{}，错误信息：{}",
                            phoneList, smsResponse.getCode(), smsResponse.getMsg());
                    return false;
                }
            } else {
                log.error("短信发送HTTP请求失败，状态码：{}", response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            log.error("短信发送异常，请求流水号：{}", request.getRequestNo(), e);
            return false;
        }
    }
}
