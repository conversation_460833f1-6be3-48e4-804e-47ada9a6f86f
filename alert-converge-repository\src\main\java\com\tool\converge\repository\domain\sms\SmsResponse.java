package com.tool.converge.repository.domain.sms;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 短信发送响应对象
 *
 * <AUTHOR>
 * @since 2025-08-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 返回码 (如: "0000" 表示成功)
     */
    private String code;

    /**
     * 描述信息 (如: "发送成功")
     */
    private String msg;

    /**
     * 请求流水号
     */
    private String requestNo;
}
